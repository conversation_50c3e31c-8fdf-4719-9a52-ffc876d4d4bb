<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="会议室" prop="roomId">
        <el-select 
          v-model="queryParams.roomId" 
          placeholder="请选择会议室" 
          clearable 
          style="width: 160px"
        >
          <el-option
            v-for="room in roomList"
            :key="room.roomId"
            :label="room.roomName"    
            :value="room.roomId"      
          />
        </el-select>
      </el-form-item>
       <el-form-item label="申请人" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable  style="min-width: 120px">
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围"> 
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['reserve:reservation:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['reserve:reservation:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Check"
          :disabled="multiple"
          @click="handleBatchApprove"
          v-hasPermi="['reserve:reservation:approve']"
        >批量通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Close"
          :disabled="multiple"
          @click="handleBatchReject"
          v-hasPermi="['reserve:reservation:approve']"
        >批量拒绝</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleForceCancel"
          v-hasPermi="['reserve:reservation:cancel']"
        >强制取消</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['reserve:reservation:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="reservationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="预约ID" align="center" prop="reservationId" />
      <el-table-column label="会议室ID" align="center" prop="roomId" />
      <el-table-column label="申请人姓名" align="center" prop="userName" />
      <el-table-column label="部门名称" align="center" prop="deptName" />
      <el-table-column label="会议主题" align="center" prop="meetingTitle" />
      <el-table-column label="会议开始时间" align="center" prop="startTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会议结束时间" align="center" prop="endTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" >
        <template #default="scope">
          <el-tag :type="statusTagType(scope.row.status)">
            {{ formatStatus(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['reserve:reservation:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['reserve:reservation:remove']">删除</el-button>
          <el-button
             v-if="scope.row.status == 1"
             link
             type="success"
             icon="Check"
             @click="handleApprove(scope.row, 2)"
             v-hasPermi="['reserve:reservation:approve']"
            >通过</el-button>
          <el-button
            link
            type="warning"
            icon="Close"
            @click="handleApprove(scope.row, 3)"
            v-hasPermi="['reserve:reservation:approve']"
            v-if="scope.row.status == 1"
          >拒绝</el-button>
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="handleForceCancel(scope.row)"
            v-hasPermi="['reserve:reservation:cancel']"
            v-if="scope.row.status != 0"
          >取消</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会议室预约申请对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="reservationRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="会议室" prop="roomId">
          <el-select v-model="form.roomId" placeholder="请选择会议室" clearable style="width: 100%">
            <el-option
              v-for="room in availableRooms"
              :key="room.roomId"
              :label="room.roomName"
              :value="room.roomId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="申请人姓名" prop="userName">
          <el-select 
            v-model="form.userName" 
            placeholder="请选择申请人" 
            filterable
            clearable
            style="width: 100%"
            @change="onUserChange"
            @focus="handleUserSelectFocus"
          >
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="user.userName"
              :value="user.userName"
            >
              <span style="float: left">{{ user.userName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ user.deptName }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请人ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入申请人ID" />
        </el-form-item>
        <el-form-item label="部门ID" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门ID" />
        </el-form-item>
        <el-form-item label="部门名称" prop="deptName">
          <el-input v-model="form.deptName" placeholder="请输入部门名称" />
        </el-form-item>
        <el-form-item label="会议主题" prop="meetingTitle">
          <el-input v-model="form.meetingTitle" placeholder="请输入会议主题" />
        </el-form-item>
        <el-form-item label="会议开始时间" prop="startTime">
          <el-date-picker clearable
            v-model="form.startTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledDate"
            placeholder="请选择会议开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="会议结束时间" prop="endTime">
          <el-date-picker clearable
            v-model="form.endTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledDate"
            placeholder="请选择会议结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批意见" prop="approveComment">
          <el-input v-model="form.approveComment" type="textarea" placeholder="请输入审批意见" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Reservation">
import { ref, reactive, toRefs, getCurrentInstance, watch } from "vue";
import { 
  listReservation, getReservation, delReservation, 
  addReservation, updateReservation, approveReservation, 
  myReservationList 
} from "@/api/reserve/reservation"; 
import { listUser } from "@/api/system/user";
import { listRoom } from "@/api/search/room";

// Vue实例代理
const { proxy } = getCurrentInstance();

// 状态选项
const statusOptions = [
  { dictLabel: '已取消', dictValue: '0' },
  { dictLabel: '待审核', dictValue: '1' },
  { dictLabel: '已通过', dictValue: '2' },
  { dictLabel: '已拒绝', dictValue: '3' },
  { dictLabel: '已完成', dictValue: '4' }
]

// UI相关
const reservationList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const activeTab = ref("all")
const myReservationListData = ref([])
//用户列表数据
const userList = ref([])
const userLoading = ref(false)
//会议室列表数据
const roomList = ref([])
const roomLoading = ref(false)
//时间范围
const dateRange = ref([])

//仅显示 status 为 1 的会议室
const availableRooms = computed(() => {
  console.log(roomList.value)
  return roomList.value.filter(room => room.status === 1)
})

// 表单数据
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 1000,
    roomId: null,
    status: null,
    userName: null,
    startTime: null,    // 开始时间参数
    endTime: null       // 结束时间参数
  },
  rules: {
    roomId: [{ required: true, message: "会议室不能为空", trigger: "blur" }],
    userId: [{ required: true, message: "申请人ID不能为空", trigger: "blur" }],
    userName: [{ required: true, message: "申请人姓名不能为空", trigger: "blur" }],
    deptId: [{ required: true, message: "部门ID不能为空", trigger: "blur" }],
    deptName: [{ required: true, message: "部门名称不能为空", trigger: "blur" }],
    meetingTitle: [{ required: true, message: "会议主题不能为空", trigger: "blur" }],
    startTime: [{ required: true, message: "会议开始时间不能为空", trigger: "blur" }],
    endTime: [{ required: true, message: "会议结束时间不能为空", trigger: "blur" }],
  }
})

const { queryParams, form, rules } = toRefs(data)

// ========== 核心方法 ==========

function getList() {
  loading.value = true
  listReservation(queryParams.value).then(response => {
    reservationList.value = response.rows
    console.log("预约的数据",response.rows)
    total.value = response.total
    loading.value = false
  })
}

function getMyList() {
  (queryParams.value).then(response => {
    myReservationListData.value = response.rows
    total.value = response.total
  })
}

/** 状态格式化 */
function formatStatus(status) {
  const map = { 0: '已取消', 1: '待审核', 2: '已通过', 3: '已拒绝', 4: '已完成' }
  return map[status] || '未知'
}

/** 状态标签类型 */
function statusTagType(status) {
  const map = { 0: 'info', 1: 'warning', 2: 'success', 3: 'danger', 4: '' }
  return map[status]
}

function handleApprove(row, status) {
  proxy.$confirm(`确认要${status === 2 ? '通过' : '拒绝'}该预约吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const data = {
      reservationId: row.reservationId,
      status: status,
      approveComment: `${status === 2 ? '通过' : '拒绝'}审批`
    }
    return approveReservation(data)
  }).then(() => {
    proxy.$modal.msgSuccess("操作成功")
    getList()
  }).catch(error => {
    if (error !== "cancel") {
      proxy.$modal.msgError("操作失败: " + (error.message || error))
    }
  })
}

function handleBatchApprove() {
  if (ids.value.length === 0) {
    proxy.$modal.msgWarning("请选择要批量通过的预约")
    return
  }
  proxy.$confirm("确认要批量通过选中的预约吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const promises = ids.value.map(id => {
      return approveReservation({
        reservationId: id,
        status: 2,
        approveComment: "批量通过审批"
      })
    })
    return Promise.all(promises)
  }).then(() => {
    proxy.$modal.msgSuccess("批量通过操作成功")
    getList()
  }).catch(error => {
    if (error !== "cancel") {
      proxy.$modal.msgError("操作失败: " + (error.message || error))
    }
  })
}

function handleBatchReject() {
  if (ids.value.length === 0) {
    proxy.$modal.msgWarning("请选择要批量拒绝的预约")
    return
  }
  proxy.$confirm("确认要批量拒绝选中的预约吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const promises = ids.value.map(id => {
      return approveReservation({
        reservationId: id,
        status: 3,
        approveComment: "批量拒绝审批"
      })
    })
    return Promise.all(promises)
  }).then(() => {
    proxy.$modal.msgSuccess("批量拒绝操作成功")
    getList()
  }).catch(error => {
    if (error !== "cancel") {
      proxy.$modal.msgError("操作失败: " + (error.message || error))
    }
  })
}

function handleForceCancel(row) {
  const _reservationIds = row ? row.reservationId : ids.value
  proxy.$confirm(`确认要${row ? '取消' : '批量取消'}选中的预约吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return approveReservation({
      reservationId: _reservationIds,
      status: 0,
      approveComment: "强制取消预约"
    })
  }).then(() => {
    proxy.$modal.msgSuccess("取消操作成功")
    getList()
  }).catch(error => {
    if (error !== "cancel") {
      proxy.$modal.msgError("操作失败: " + (error.message || error))
    }
  })
}

function cancel() {
  open.value = false
  reset()
}

function reset() {
  form.value = {
    reservationId: null,
    roomId: null,
    roomName: null,
    userId: null,
    userName: null,
    deptId: null,
    deptName: null,
    meetingTitle: null,
    startTime: null,
    endTime: null,
    attendees: null,
    status: null,
    remark: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("reservationRef")
}

function handleQuery() {
  queryParams.value.pageNum = 1
  
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.value.startTime = dateRange.value[0] + ' 00:00:00'  
    queryParams.value.endTime = dateRange.value[1] + ' 23:59:59'   
  } else {
    queryParams.value.startTime = null   // 改为 startTime
    queryParams.value.endTime = null
  }
  
  // 清除旧的参数名（如果存在）
  queryParams.value.beginTime = null
  
  console.log('🔍 修正后的查询参数:', queryParams.value)
  getList()
}


function resetQuery() {
  dateRange.value = []  // 重置日期范围
  proxy.resetForm("queryRef")
  handleQuery()
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.reservationId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

function handleAdd() {
  reset()
  open.value = true
  title.value = "添加会议室预约申请"
}

function handleUpdate(row) {
  reset()
  const _reservationId = row.reservationId || ids.value
  
  // 确保用户列表已加载
  if (userList.value.length === 0) {
    getUserList()
  }
  
  getReservation(_reservationId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改会议室预约申请"
  })
}

function submitForm() {
  proxy.$refs["reservationRef"].validate(valid => {
    if (valid) {
      console.log('🔍 提交前的 form.value:', form.value)
      console.log('🔍 startTime 类型:', typeof form.value.startTime, form.value.startTime)
      console.log('🔍 endTime 类型:', typeof form.value.endTime, form.value.endTime)

      form.value.roomId = Number(form.value.roomId);

      if (form.value.reservationId != null) {
        updateReservation(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        console.log('将要进入add预约的form.value', form.value)
        addReservation(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

function handleDelete(row) {
  const _reservationIds = row.reservationId || ids.value
  proxy.$modal.confirm(`是否确认删除会议室预约申请编号为 "${_reservationIds}" 的数据项？`)
    .then(() => delReservation(_reservationIds))
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    }).catch(() => {})
}

function handleExport() {
  proxy.download('reserve/reservation/export', {
    ...queryParams.value
  }, `reservation_${new Date().getTime()}.xlsx`)
}

/** 禁用过去的日期 */
function disabledDate(time) {
  return time.getTime() < Date.now() - 8.64e7 // 禁用今天之前的日期
}

// 获取用户列表的方法
async function getUserList() {
  try {
    userLoading.value = true
    
    // 调用用户列表API，获取所有活跃用户
    const response = await listUser({
      pageNum: 1,
      pageSize: 1000,  // 获取所有用户
      status: '0'      // 只获取正常状态的用户
    })
    
    userList.value = response.rows || []
    console.log('获取到用户列表:', userList.value) // 调试用
    
  } catch (error) {
    console.error('获取用户列表失败:', error)
    proxy.$modal.msgError('获取用户列表失败')
    userList.value = []
  } finally {
    userLoading.value = false
  }
}

// 用户选择变化时的处理方法
function onUserChange(userName) {
  if (!userName) {
    // 清空时重置相关字段
    form.value.userId = null
    form.value.deptId = null
    form.value.deptName = null
    return
  }
  
  // 根据用户名找到对应的用户信息
  const selectedUser = userList.value.find(user => user.userName === userName)
  
  if (selectedUser) {
    // 自动填写关联字段
    form.value.userId = selectedUser.userId
    form.value.deptId = selectedUser.deptId
    form.value.deptName = selectedUser.dept?.deptName || selectedUser.deptName || '未知部门'
    
    console.log('选择的用户信息:', selectedUser) // 调试用
    
    // 显示提示信息
    proxy.$modal.msgSuccess(`已选择用户：${selectedUser.userName} (${form.value.deptName})`)
  } else {
    proxy.$modal.msgWarning('未找到用户信息')
  }
}

// 处理用户选择框获得焦点
function handleUserSelectFocus() {
  // 如果用户列表为空，则加载用户列表
  if (userList.value.length === 0) {
    getUserList()
  }
}

// 获取会议室列表的方法 
async function getRoomList() {
  try {
    roomLoading.value = true
    
    console.log('开始获取会议室列表...')
    
    // 修改：使用空对象作为参数，和工作正常的文件保持一致
    const response = await listRoom({pageNum: 1, pageSize: 1000})
    
    console.log('会议室API响应:', response)
    roomList.value = response.rows || response.data || []
    
    console.log('最终会议室列表:', roomList.value)
    
  } catch (error) {
    console.error('获取会议室列表失败:', error)
    proxy.$modal.msgError('获取会议室列表失败')
    roomList.value = []
  } finally {
    roomLoading.value = false
  }
}


// 处理会议室选择框获得焦点
function handleRoomSelectFocus() {
  // 如果会议室列表为空，则加载会议室列表
  if (roomList.value.length === 0) {
    getRoomList()
  }
}



// 监听 tab 切换
watch(activeTab, (val) => {
  if (val === 'my') {
    getMyList()
  }
})

// 初始化数据
getList()
getUserList() 
getRoomList()
</script>
